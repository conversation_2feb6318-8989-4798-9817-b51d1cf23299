// Copyright 2025 TiKV Project Authors. Licensed under Apache-2.0.

use std::{convert::Try<PERSON>rom, io::Write};

use anyhow::{anyhow, bail, Result};
use bytes::BufMut;
use hexhex::hex;
use kvenginepb::fts as ftspb;
use protobuf::Message;
use xorf::BinaryFuse8;

use super::PackedFileFooter;
use crate::{codecutil::next_aligned_offset, table::ChecksumType};

/// Options for building a `PackedFile`.
pub struct PackedFileBuilderOptions {
    /// The target size of data blocks. A block can be larger if a single
    /// logical partition exceeds this size. Defaults to 64 KiB.
    pub block_size: usize,
    /// The checksum algorithm to use.
    pub checksum_type: ChecksumType,
}

impl Default for PackedFileBuilderOptions {
    fn default() -> Self {
        Self {
            block_size: 64 * 1024, // 64 KiB
            checksum_type: ChecksumType::Crc32c,
        }
    }
}

/// Holds the internal state for building a single logical partition.
/// This struct is designed to group state and be reusable via `reset`.
#[derive(Default)]
struct CurrentLP {
    is_touched: bool,
    is_int_pk: bool,
    lp_key: Vec<u8>,

    tantivy_index_data: Vec<u8>,

    // Data for building the PK filter
    pk_hashes: Vec<u64>,

    // PK data stored in efficient flat buffers
    pks_int: Vec<u64>,
    pk_common_data: Vec<u8>,
    // n_pk + 1 elements. For a blank LP it always has one element, which is always 0.
    // For each new PK, the end offset is added.
    pk_common_offsets: Vec<u32>,

    // Other per-PK metadata
    versions: Vec<u64>,
    delete_marks: Vec<u8>,
    pk_count: u32,

    // State for order checking
    last_pk_int: u64,
    last_pk_common: Vec<u8>,
    last_version: u64,

    // Serialized data for the current LP.
    serialized_buf: Vec<u8>,
}

impl CurrentLP {
    /// Resets the state for a new logical partition.
    fn clear(&mut self) {
        self.is_touched = false;
        self.is_int_pk = false;
        self.lp_key.clear();
        self.tantivy_index_data.clear();
        self.pk_hashes.clear();
        self.pks_int.clear();
        self.pk_common_data.clear();
        self.pk_common_offsets.clear();
        self.versions.clear();
        self.delete_marks.clear();
        self.pk_count = 0;
        self.last_pk_int = 0;
        self.last_pk_common.clear();
        self.last_version = 0;
        self.serialized_buf.clear();
    }
}

#[derive(Default)]
struct CurrentDataBlock {
    offset: u64,
    checksum: u32,

    // n_entries + 1 elements. For a blank data block it always have one element,
    // which is the (supposed) offset of the first entry (0).
    // For each new entry, the end offset is added.
    entry_offsets: Vec<u32>,

    /// A buffer to be reused for serializing data block metadata.
    meta_ser_buf: Vec<u8>,
}

impl CurrentDataBlock {
    /// Resets the state for a new data block.
    fn clear(&mut self) {
        self.offset = 0;
        self.checksum = 0;
        self.entry_offsets.clear();
        self.meta_ser_buf.clear();
    }
}

trait WrittenSize {
    /// Returns the size of the data that has been written so far.
    fn written_size(&self) -> usize;
}

impl WrittenSize for Vec<u8> {
    fn written_size(&self) -> usize {
        self.len()
    }
}

/// A builder for creating FTS PackedFiles.
///
/// The user is responsible for feeding data in the correct order:
/// 1. Logical partitions must be added in ascending order of their keys.
/// 2. Within an LP, primary keys must be added in ascending order.
/// 3. For a given PK, versions must be added in descending order.
///
/// The builder checks for this order and returns an error if it's violated.
/// It buffers at most one LP's data to minimize memory usage.
pub struct PackedFileBuilder<W: Write> {
    writer: W,
    options: PackedFileBuilderOptions,

    // Global file state
    offset: u64,
    footer: PackedFileFooter,
    index_block: ftspb::PackedFileIndexBlock,
    lp_key_hashes: Vec<u64>, // For building the final LP filter
    props: ftspb::PackedFilePropertyBlock,

    // For checking order of LP keys
    last_lp_key: Vec<u8>,

    // State for the current data block being built
    this_data_block: CurrentDataBlock,

    // State for the current logical partition being built
    this_lp: CurrentLP,
}

impl<W: Write> PackedFileBuilder<W> {
    /// Creates a new `PackedFileBuilder`.
    pub fn new(writer: W, options: PackedFileBuilderOptions) -> Self {
        let mut footer = PackedFileFooter::new();
        footer.checksum_type = options.checksum_type;

        Self {
            writer,
            options,
            offset: 0,
            footer,
            index_block: ftspb::PackedFileIndexBlock::new(),
            lp_key_hashes: Vec::new(),
            props: ftspb::PackedFilePropertyBlock::new(),
            last_lp_key: Vec::new(),
            this_data_block: CurrentDataBlock::default(),
            this_lp: CurrentLP::default(),
        }
    }

    /// Starts building a new logical partition with the given key.
    /// This will automatically finalize the previous LP if one was in progress.
    pub fn start_lp(&mut self, is_int_pk: bool, lp_key: &[u8]) -> Result<()> {
        if self.this_lp.is_touched {
            bail!("Must call finish_lp() before starting a new one.");
        }
        if lp_key.is_empty() {
            bail!("Logical partition key cannot be empty.");
        }
        if !self.last_lp_key.is_empty() && lp_key <= self.last_lp_key.as_slice() {
            bail!(
                "LP keys must be added in ascending order. Last: {}, new: {}",
                hex(&self.last_lp_key),
                hex(lp_key)
            );
        }

        self.this_lp.is_touched = true;
        self.this_lp.is_int_pk = is_int_pk;
        self.this_lp.lp_key.extend_from_slice(lp_key);
        self.this_lp.pk_common_offsets.push(0); // The first offset is always 0.

        Ok(())
    }

    /// Adds a primary key to the current logical partition (for int PK).
    pub fn add_pk_int(&mut self, pk: u64, version: u64, is_delete: bool) -> Result<()> {
        if !self.this_lp.is_touched {
            bail!("Must call start_lp() before adding PKs.");
        }
        if !self.this_lp.is_int_pk {
            bail!("Expected int PK, but current LP is set for common PKs.");
        }
        if self.this_lp.pk_count > 0 {
            if pk < self.this_lp.last_pk_int {
                bail!("PKs must be in ascending order.");
            }
            if pk == self.this_lp.last_pk_int && version >= self.this_lp.last_version {
                bail!("Versions for the same PK must be in descending order.");
            }
        }

        self.this_lp.pks_int.push(pk);
        self.this_lp.versions.push(version);
        self.this_lp.delete_marks.push(is_delete as u8);
        self.this_lp
            .pk_hashes
            .push(farmhash::fingerprint64(&pk.to_le_bytes()));
        self.this_lp.pk_count = self
            .this_lp
            .pk_count
            .checked_add(1)
            .ok_or_else(|| anyhow!("Too many PKs in a single LP"))?;
        self.this_lp.last_pk_int = pk;
        self.this_lp.last_version = version;

        Ok(())
    }

    /// Adds a primary key to the current logical partition (for common PK).
    pub fn add_pk_common(&mut self, pk: &[u8], version: u64, is_delete: bool) -> Result<()> {
        if !self.this_lp.is_touched {
            bail!("Must call start_lp() before adding PKs.");
        }
        if self.this_lp.is_int_pk {
            bail!("Expected common PK, but current LP is set for int PKs.");
        }
        if pk.is_empty() {
            bail!("Common PK cannot be empty.");
        }
        if self.this_lp.pk_count > 0 {
            if pk < self.this_lp.last_pk_common.as_slice() {
                bail!("PKs must be in ascending order.");
            }
            if pk == self.this_lp.last_pk_common.as_slice() && version >= self.this_lp.last_version
            {
                bail!("Versions for the same PK must be in descending order.");
            }
        }

        self.this_lp.pk_common_data.extend_from_slice(pk);
        self.this_lp
            .pk_common_offsets
            .push(u32::try_from(self.this_lp.pk_common_data.len())?);
        self.this_lp.versions.push(version);
        self.this_lp.delete_marks.push(is_delete as u8);
        self.this_lp.pk_hashes.push(farmhash::fingerprint64(pk));
        self.this_lp.pk_count = self
            .this_lp
            .pk_count
            .checked_add(1)
            .ok_or_else(|| anyhow!("Too many PKs in a single LP"))?;
        self.this_lp.last_pk_common.clear();
        self.this_lp.last_pk_common.extend_from_slice(pk);
        self.this_lp.last_version = version;

        Ok(())
    }

    /// Sets the Tantivy index data for the current logical partition.
    pub fn set_tantivy_index(&mut self, data: &[u8]) -> Result<()> {
        if !self.this_lp.is_touched {
            bail!("Must call start_lp() before setting index data.");
        }
        self.this_lp.tantivy_index_data.clear();
        self.this_lp.tantivy_index_data.extend_from_slice(data);
        Ok(())
    }

    /// Serializes all data for the current LP into `this_lp.serialized_buf`.
    fn serialize_lp_entry(&mut self) -> Result<()> {
        if !self.this_lp.is_touched {
            bail!("Must call start_lp() before serializing LP.");
        }

        let lp = &mut self.this_lp;
        let buf = &mut lp.serialized_buf;
        buf.clear();

        if lp.lp_key.is_empty() {
            bail!("LP key cannot be empty.");
        }
        if lp.pk_count == 0 {
            bail!("LP must have at least one PK.");
        }

        // --- LP Key ---
        buf.put_u16_le(u16::try_from(lp.lp_key.len())?);
        buf.put_slice(&lp.lp_key);

        // --- PK count and type flag ---
        buf.put_u32_le(lp.pk_count);
        buf.put_u8(lp.is_int_pk as u8);
        Self::write_align_padding(buf, 8);

        // --- Versions and Delete Marks ---
        buf.put_slice(bytemuck::cast_slice(&lp.versions));
        buf.put_slice(&lp.delete_marks);
        Self::write_align_padding(buf, 4);

        // --- Common PK Offsets (if applicable) ---
        if !lp.is_int_pk {
            buf.put_slice(bytemuck::cast_slice(&lp.pk_common_offsets));
        }
        Self::write_align_padding(buf, 8);

        // --- PK Data ---
        if lp.is_int_pk {
            buf.put_slice(bytemuck::cast_slice(&lp.pks_int));
        } else {
            buf.put_slice(&lp.pk_common_data);
        }

        // --- PK Filter ---
        // Deduplicate PK hashes since the same PK can have multiple versions
        let mut unique_pk_hashes = lp.pk_hashes.clone();
        unique_pk_hashes.sort_unstable();
        unique_pk_hashes.dedup();

        let pk_filter = match BinaryFuse8::try_from(&unique_pk_hashes) {
            Ok(filter) => filter,
            Err(e) => bail!(
                "Failed to build PK filter for LP {}: {}",
                hex(&lp.lp_key),
                e
            ),
        };
        let filter_data = pk_filter.to_vec();
        buf.put_u32_le(u32::try_from(filter_data.len())?);
        buf.put_slice(&filter_data);

        // --- Tantivy Index Data ---
        buf.put_u32_le(u32::try_from(lp.tantivy_index_data.len())?);
        buf.put_slice(&lp.tantivy_index_data);

        // --- End padding ---
        Self::write_align_padding(buf, 8);

        Ok(())
    }

    /// Serializes the current LP data and adds it to the current data block
    /// buffer.
    pub fn finish_lp(&mut self) -> Result<()> {
        if !self.this_lp.is_touched {
            bail!("No active LP to finish.");
        }

        self.serialize_lp_entry()?;
        let entry_len = self.this_lp.serialized_buf.len();

        // Each data block records the start key of the first LP in it.
        // So we can only start a new data block after the first LP is added.
        if self.this_data_block.entry_offsets.is_empty() {
            self.start_data_block(self.this_lp.lp_key.to_vec())?;
        }

        // At this moment we expect paddings have already been added properly.
        // The Padding before entry should be added in try_start_data_block
        // and maintained by each finish_lp() call.
        // The Padding after entry should be added in serialize_lp_entry.
        if self.offset % 8 != 0 {
            bail!(
                "Runtime error: Unexpected file offset alignment: offset={}",
                self.offset
            );
        }
        if entry_len % 8 != 0 {
            bail!(
                "Runtime error: Unexpected LP entry length alignment: len={}",
                entry_len
            );
        }

        self.writer.write_all(&self.this_lp.serialized_buf)?;
        self.offset += entry_len as u64;
        self.this_data_block.offset += entry_len as u64;
        self.this_data_block.checksum = self
            .options
            .checksum_type
            .append(self.this_data_block.checksum, &self.this_lp.serialized_buf);
        self.this_data_block
            .entry_offsets
            .push(u32::try_from(self.this_data_block.offset)?);

        // Add LP key hash for the file-level filter.
        self.lp_key_hashes
            .push(farmhash::fingerprint64(&self.this_lp.lp_key));

        // Update the last seen LP key for order checking.
        self.last_lp_key.clear();
        self.last_lp_key.extend_from_slice(&self.this_lp.lp_key);

        // Clear the current LP state for the next one.
        self.this_lp.clear();

        // Flush the data block if it's large enough.
        if self.this_data_block.offset >= self.options.block_size as u64 {
            self.finish_data_block()?;
        }

        Ok(())
    }

    /// Starts a new data block.
    /// It will write initial padding to the underlying writer to make sure
    /// inner entries are aligned to 8 bytes.
    /// It will also record this data block in the index block.
    fn start_data_block(&mut self, lp_key: Vec<u8>) -> Result<()> {
        // Align this data block's offset to 8 bytes.
        // The initial padding is included in the checksum, but not included in the
        // entry offsets.
        let padding = [0u8; 8];
        let pad_len = next_aligned_offset(self.offset as usize, 8) - (self.offset as usize);
        self.writer.write_all(&padding[..pad_len])?;
        self.offset += pad_len as u64;

        self.this_data_block.offset += pad_len as u64;
        self.this_data_block.checksum = self
            .options
            .checksum_type
            .append(self.this_data_block.checksum, &padding[..pad_len]);
        self.this_data_block
            .entry_offsets
            .push(u32::try_from(self.this_data_block.offset)?);

        self.index_block
            .data_block_offsets
            .push(u32::try_from(self.offset)?);
        self.index_block.data_block_start_keys.push(lp_key);

        Ok(())
    }

    /// Finish this data block. It will write data block "footer" to the
    /// underlying writer.
    fn finish_data_block(&mut self) -> Result<()> {
        if self.this_data_block.entry_offsets.is_empty() {
            // This should not happen, because we should always call
            // `try_start_data_block()` before adding any entries.
            bail!("Runtime error: Unexpected empty data block.");
        }
        if self.offset % 8 != 0 {
            // This should not happen, because each entry has been padded to 8 bytes
            // at the end.
            bail!(
                "Runtime error: Unexpected file offset alignment: offset={}",
                self.offset
            );
        }

        // We first serialize metadata into a buffer, then write it to the writer.
        let buf = &mut self.this_data_block.meta_ser_buf;
        buf.clear();

        // --- Entry Offsets ---
        buf.put_slice(bytemuck::cast_slice(&self.this_data_block.entry_offsets));

        // --- Entry Count ---
        // There are n+1 elements in entry_offsets
        let n_entries = self.this_data_block.entry_offsets.len() - 1;
        buf.put_u32_le(u32::try_from(n_entries)?);

        // --- Checksum ---
        // Checksum should include everything before the checksum field, including the
        // metadata.
        self.this_data_block.checksum = self
            .options
            .checksum_type
            .append(self.this_data_block.checksum, buf);
        buf.put_u32_le(self.this_data_block.checksum);

        // --- Write the data block metadata to the writer ---
        self.writer.write_all(buf)?;
        self.offset += buf.len() as u64;

        // Clear the current data block.
        self.this_data_block.clear();

        Ok(())
    }

    /// Finalizes the build process, writing all remaining buffered data,
    /// metadata blocks, and the footer to the writer. Returns the total
    /// size of the file.
    pub fn finish(mut self) -> Result<u64> {
        if self.this_lp.is_touched {
            bail!("Must call finish_lp() before finishing the file.");
        }
        if !self.this_data_block.entry_offsets.is_empty() {
            // Finish the last data block if it has data.
            // This is allowed, because a data block is only finalized
            // when it exceeds the block size.
            self.finish_data_block()?;
        }

        // Index block additionally contains the end offset of the last data block.
        self.index_block
            .data_block_offsets
            .push(u32::try_from(self.offset)?);

        // Checksum for index+filter+props.
        let mut meta_checksum = 0;

        // --- Write Index Block ---
        self.footer.index_block_offset = u32::try_from(self.offset)?;
        let index_data = self.index_block.write_to_bytes()?;
        self.writer.write_all(&index_data)?;
        self.offset += index_data.len() as u64;
        meta_checksum = self
            .options
            .checksum_type
            .append(meta_checksum, &index_data);

        // --- Write LP Filter Block ---
        self.footer.lp_filter_block_offset = u32::try_from(self.offset)?;
        let lp_filter = BinaryFuse8::try_from(&self.lp_key_hashes)
            .map_err(|e| anyhow!("Failed to build LP filter: {}", e))?;
        let filter_data = lp_filter.to_vec();
        self.writer.write_all(&filter_data)?;
        self.offset += filter_data.len() as u64;
        meta_checksum = self
            .options
            .checksum_type
            .append(meta_checksum, &filter_data);

        // --- Write Property Block ---
        self.footer.prop_offset = u32::try_from(self.offset)?;
        let props_data = self.props.write_to_bytes()?;
        if !props_data.is_empty() {
            self.writer.write_all(&props_data)?;
            self.offset += props_data.len() as u64;
            meta_checksum = self
                .options
                .checksum_type
                .append(meta_checksum, &props_data);
        }

        // --- Write Footer ---
        self.footer.checksum_other_meta = meta_checksum;
        let n = self.footer.marshal(&mut self.writer)?;
        self.offset += n as u64;

        Ok(self.offset)
    }

    /// Appends zero-padding to a buffer to meet an alignment requirement.
    fn write_align_padding<B: BufMut + WrittenSize>(buf: &mut B, align: usize) {
        let written = buf.written_size();
        let next_offset = next_aligned_offset(written, align);
        if next_offset > written {
            buf.put_bytes(0, next_offset - written);
        }
    }
}

#[cfg(test)]
mod tests {
    // Additional comprehensive tests for builder and roundtrip functionality
    use std::io::Cursor;

    use super::*;

    #[test]
    fn test_builder_error_empty_lp_key() {
        let mut buffer = Vec::new();
        let mut builder = PackedFileBuilder::new(
            Cursor::new(&mut buffer),
            PackedFileBuilderOptions::default(),
        );

        let result = builder.start_lp(true, b"");
        assert!(result.is_err());
        assert!(
            result
                .unwrap_err()
                .to_string()
                .contains("Logical partition key cannot be empty")
        );
    }

    #[test]
    fn test_builder_error_lp_key_order() {
        let mut buffer = Vec::new();
        let mut builder = PackedFileBuilder::new(
            Cursor::new(&mut buffer),
            PackedFileBuilderOptions::default(),
        );

        builder.start_lp(true, b"lp2").unwrap();
        builder.add_pk_int(1, 100, false).unwrap();
        builder.finish_lp().unwrap();

        // Try to add LP with smaller key
        let result = builder.start_lp(true, b"lp1");
        assert!(result.is_err());
        assert!(
            result
                .unwrap_err()
                .to_string()
                .contains("LP keys must be added in ascending order")
        );
    }

    #[test]
    fn test_builder_error_pk_without_lp() {
        let mut buffer = Vec::new();
        let mut builder = PackedFileBuilder::new(
            Cursor::new(&mut buffer),
            PackedFileBuilderOptions::default(),
        );

        let result = builder.add_pk_int(1, 100, false);
        assert!(result.is_err());
        assert!(
            result
                .unwrap_err()
                .to_string()
                .contains("Must call start_lp")
        );
    }

    #[test]
    fn test_builder_error_pk_type_mismatch() {
        let mut buffer = Vec::new();
        let mut builder = PackedFileBuilder::new(
            Cursor::new(&mut buffer),
            PackedFileBuilderOptions::default(),
        );

        builder.start_lp(true, b"lp1").unwrap(); // int PK

        let result = builder.add_pk_common(b"pk1", 100, false);
        assert!(result.is_err());
        assert!(
            result
                .unwrap_err()
                .to_string()
                .contains("Expected common PK")
        );
    }

    #[test]
    fn test_builder_error_pk_order() {
        let mut buffer = Vec::new();
        let mut builder = PackedFileBuilder::new(
            Cursor::new(&mut buffer),
            PackedFileBuilderOptions::default(),
        );

        builder.start_lp(true, b"lp1").unwrap();
        builder.add_pk_int(2, 100, false).unwrap();

        // Try to add smaller PK
        let result = builder.add_pk_int(1, 99, false);
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("ascending order"));
    }

    #[test]
    fn test_builder_error_version_order() {
        let mut buffer = Vec::new();
        let mut builder = PackedFileBuilder::new(
            Cursor::new(&mut buffer),
            PackedFileBuilderOptions::default(),
        );

        builder.start_lp(true, b"lp1").unwrap();
        builder.add_pk_int(1, 100, false).unwrap();

        // Try to add larger version for same PK
        let result = builder.add_pk_int(1, 101, false);
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("descending order"));
    }

    #[test]
    fn test_builder_error_empty_common_pk() {
        let mut buffer = Vec::new();
        let mut builder = PackedFileBuilder::new(
            Cursor::new(&mut buffer),
            PackedFileBuilderOptions::default(),
        );

        builder.start_lp(false, b"lp1").unwrap(); // common PK

        let result = builder.add_pk_common(b"", 100, false);
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("cannot be empty"));
    }

    #[test]
    fn test_builder_error_finish_without_lp() {
        let mut buffer = Vec::new();
        let mut builder = PackedFileBuilder::new(
            Cursor::new(&mut buffer),
            PackedFileBuilderOptions::default(),
        );

        let result = builder.finish_lp();
        assert!(result.is_err());
        assert!(result.unwrap_err().to_string().contains("No active LP"));
    }

    #[test]
    fn test_builder_error_finish_file_with_active_lp() {
        let mut buffer = Vec::new();
        let mut builder = PackedFileBuilder::new(
            Cursor::new(&mut buffer),
            PackedFileBuilderOptions::default(),
        );

        builder.start_lp(true, b"lp1").unwrap();
        builder.add_pk_int(1, 100, false).unwrap();
        // Don't finish LP

        let result = builder.finish();
        assert!(result.is_err());
        assert!(
            result
                .unwrap_err()
                .to_string()
                .contains("Must call finish_lp")
        );
    }

    #[test]
    fn test_builder_error_start_lp_without_finishing() {
        let mut buffer = Vec::new();
        let mut builder = PackedFileBuilder::new(
            Cursor::new(&mut buffer),
            PackedFileBuilderOptions::default(),
        );

        builder.start_lp(true, b"lp1").unwrap();
        builder.add_pk_int(1, 100, false).unwrap();

        // Try to start another LP without finishing the first
        let result = builder.start_lp(true, b"lp2");
        assert!(result.is_err());
        assert!(
            result
                .unwrap_err()
                .to_string()
                .contains("Must call finish_lp")
        );
    }
}
